"""Volume anomaly detection and analysis service."""

import logging
from datetime import datetime, timezone, timedelta
from typing import List
from sqlalchemy import text
from app.models import VolumeAnomaly, LargeSwap
from app.database import SessionLocal

logger = logging.getLogger(__name__)

class VolumeAnalyzer:
    """Service for detecting volume anomalies and analyzing trading patterns."""

    # Thresholds for significant trades (in lamports) - lowered for pumpfun tokens
    LARGE_TRADE_THRESHOLD = 500_000_000   # 0.5 SOL (lowered from 1 SOL)
    WHALE_TRADE_THRESHOLD = 5_000_000_000  # 5 SOL (lowered from 10 SOL)

    # Minimum volume thresholds for anomaly detection (in SOL) - optimized for short timeframes
    MIN_VOLUME_5M = 0.01   # Minimum 0.01 SOL volume in 5min to be considered
    MIN_VOLUME_15M = 0.05  # Minimum 0.05 SOL volume in 15min to be considered
    MIN_VOLUME_30M = 0.1   # Minimum 0.1 SOL volume in 30min to be considered

    # Anomaly score thresholds - more sensitive for rapid detection
    MIN_ANOMALY_SCORE = 1.5  # Minimum 1.5x increase (lowered from 2x)
    HIGH_ANOMALY_SCORE = 3.0  # 3x increase is highly anomalous (lowered from 5x)

    # Maximum age for transactions to consider (in minutes)
    MAX_TRANSACTION_AGE_MINUTES = 60  # Only process transactions from last hour

    def __init__(self):
        self.db = SessionLocal()

    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()

    def get_volume_anomalies(self, limit: int = 10, recent_transactions: int = 1000) -> List[VolumeAnomaly]:
        """Get tokens with volume anomalies by analyzing the most recent transactions."""
        try:
            now = datetime.now(timezone.utc)
            max_age_ago = now - timedelta(minutes=self.MAX_TRANSACTION_AGE_MINUTES)

            # Convert thresholds to lamports for SQL
            min_volume_5m_lamports = int(self.MIN_VOLUME_5M * 1_000_000_000)
            min_volume_15m_lamports = int(self.MIN_VOLUME_15M * 1_000_000_000)
            min_volume_30m_lamports = int(self.MIN_VOLUME_30M * 1_000_000_000)

            # Simple query to analyze the most recent transactions and find anomalous volume
            query = text("""
                WITH recent_swaps AS (
                    -- Get the most recent transactions within our time window
                    SELECT
                        s.mint_address,
                        t.name as token_name,
                        t.symbol as token_symbol,
                        s.sol_amount,
                        s.token_amount,
                        s.timestamp,
                        ROW_NUMBER() OVER (ORDER BY s.timestamp DESC) as row_num
                    FROM swaps s
                    JOIN tokens t ON s.mint_address = t.mint_address
                    WHERE s.timestamp >= :max_age_ago
                    ORDER BY s.timestamp DESC
                    LIMIT :recent_transactions
                ),
                token_stats AS (
                    -- Calculate volume and activity stats for each token
                    SELECT
                        mint_address,
                        token_name,
                        token_symbol,
                        COUNT(*) as total_swaps,
                        SUM(sol_amount) as total_volume,
                        AVG(sol_amount) as avg_swap_size,
                        MAX(sol_amount) as largest_swap,
                        MAX(timestamp) as last_activity,
                        -- Calculate volume in different time windows
                        SUM(CASE WHEN timestamp >= NOW() - INTERVAL '5 minutes' THEN sol_amount ELSE 0 END) as volume_5m,
                        SUM(CASE WHEN timestamp >= NOW() - INTERVAL '15 minutes' THEN sol_amount ELSE 0 END) as volume_15m,
                        SUM(CASE WHEN timestamp >= NOW() - INTERVAL '30 minutes' THEN sol_amount ELSE 0 END) as volume_30m,
                        COUNT(CASE WHEN timestamp >= NOW() - INTERVAL '5 minutes' THEN 1 END) as swaps_5m,
                        COUNT(CASE WHEN timestamp >= NOW() - INTERVAL '15 minutes' THEN 1 END) as swaps_15m,
                        COUNT(CASE WHEN timestamp >= NOW() - INTERVAL '30 minutes' THEN 1 END) as swaps_30m,
                        -- Calculate recent average price for market cap estimation (using recent swaps)
                        AVG(CASE WHEN timestamp >= NOW() - INTERVAL '10 minutes' AND token_amount > 0
                             THEN sol_amount::float / token_amount ELSE NULL END) as recent_price_per_token
                    FROM recent_swaps
                    GROUP BY mint_address, token_name, token_symbol
                    HAVING COUNT(*) >= 2  -- Lowered from 3 to 2 swaps to catch more tokens
                )
                SELECT
                    mint_address,
                    token_name,
                    token_symbol,
                    total_swaps,
                    total_volume,
                    avg_swap_size,
                    largest_swap,
                    last_activity,
                    volume_5m,
                    volume_15m,
                    volume_30m,
                    swaps_5m,
                    swaps_15m,
                    swaps_30m,
                    recent_price_per_token,
                    -- Simple anomaly score: recent volume / average swap size / time factor
                    CASE
                        WHEN avg_swap_size > 0 AND volume_5m > 0
                        THEN (volume_5m / avg_swap_size) * (swaps_5m + 1) *
                             (1.0 + (1.0 - EXTRACT(EPOCH FROM (NOW() - last_activity)) / 1800.0))
                        ELSE 0
                    END as anomaly_score
                FROM token_stats
                WHERE (volume_5m >= :min_volume_5m OR volume_15m >= :min_volume_15m OR volume_30m >= :min_volume_30m)
                  AND last_activity >= NOW() - INTERVAL '15 minutes'  -- Extended to 15 minutes to catch more tokens
                ORDER BY anomaly_score DESC
                LIMIT :limit
            """)

            result = self.db.execute(query, {
                'max_age_ago': max_age_ago,
                'recent_transactions': recent_transactions,
                'min_volume_5m': min_volume_5m_lamports,
                'min_volume_15m': min_volume_15m_lamports,
                'min_volume_30m': min_volume_30m_lamports,
                'limit': limit
            })

            anomalies = []
            for row in result:
                # Use the calculated anomaly score from the query
                overall_score = float(row.anomaly_score)

                # Only include if meets minimum anomaly threshold
                if overall_score >= self.MIN_ANOMALY_SCORE:
                    # Calculate individual scores for compatibility (simple ratios)
                    avg_swap = max(float(row.avg_swap_size), 1_000_000)  # Minimum 0.001 SOL
                    score_5m = float(row.volume_5m) / avg_swap if row.volume_5m > 0 else 0
                    score_15m = float(row.volume_15m) / avg_swap if row.volume_15m > 0 else 0
                    score_30m = float(row.volume_30m) / avg_swap if row.volume_30m > 0 else 0

                    # Calculate market cap estimate
                    market_cap_estimate = None
                    try:
                        if hasattr(row, 'recent_price_per_token') and row.recent_price_per_token and row.recent_price_per_token > 0:
                            # Estimate market cap: price_per_token * total_supply
                            # For pumpfun tokens, assume ~1B total supply (typical)
                            # Convert price from lamports per token to SOL per token, then to USD
                            price_sol_per_token = float(row.recent_price_per_token) / 1_000_000_000
                            estimated_supply = 1_000_000_000  # 1B tokens (typical pumpfun)
                            sol_price_usd = 200  # Rough SOL price estimate (could be made dynamic)
                            market_cap_estimate = price_sol_per_token * estimated_supply * sol_price_usd

                            # Ensure it's a valid number
                            if not isinstance(market_cap_estimate, (int, float)) or market_cap_estimate <= 0:
                                market_cap_estimate = None
                    except Exception as e:
                        logger.debug(f"Error calculating market cap for {row.mint_address}: {e}")
                        market_cap_estimate = None

                    anomalies.append(VolumeAnomaly(
                        mint_address=row.mint_address,
                        token_name=row.token_name,
                        token_symbol=row.token_symbol,
                        volume_1h=float(row.volume_30m) / 1_000_000_000,  # Use 30m as "1h" for compatibility
                        volume_6h=float(row.volume_15m) / 1_000_000_000,  # Use 15m as "6h" for compatibility
                        volume_24h=float(row.volume_5m) / 1_000_000_000,  # Use 5m as "24h" for compatibility
                        avg_volume_1h=avg_swap / 1_000_000_000,  # Use avg swap size as baseline
                        avg_volume_6h=avg_swap / 1_000_000_000,
                        avg_volume_24h=avg_swap / 1_000_000_000,
                        anomaly_score_1h=score_30m,  # Map to existing fields for compatibility
                        anomaly_score_6h=score_15m,
                        anomaly_score_24h=score_5m,
                        overall_anomaly_score=overall_score,
                        swap_count_1h=row.swaps_30m,
                        swap_count_6h=row.swaps_15m,
                        swap_count_24h=row.swaps_5m,
                        largest_swap_1h=float(row.largest_swap) / 1_000_000_000,
                        last_activity=row.last_activity,
                        market_cap_estimate=market_cap_estimate
                    ))

            return anomalies

        except Exception as e:
            logger.error(f"Error getting volume anomalies: {e}")
            return []

    def get_large_swaps_for_token(self, mint_address: str, limit: int = 5) -> List[LargeSwap]:
        """Get recent swaps for a specific token using optimized query with short timeframes."""
        try:
            now = datetime.now(timezone.utc)
            max_age_ago = now - timedelta(minutes=self.MAX_TRANSACTION_AGE_MINUTES)

            # First try to get large swaps, then fall back to any recent swaps
            query = text("""
                SELECT
                    s.signature,
                    s.timestamp,
                    s.mint_address,
                    s.token_amount,
                    s.sol_amount,
                    s.price_per_token,
                    s.swap_type,
                    s.user_wallet,
                    t.name as token_name,
                    t.symbol as token_symbol,
                    t.decimals as token_decimals,
                    EXTRACT(EPOCH FROM (:now - s.timestamp)) as seconds_ago
                FROM swaps s
                JOIN tokens t ON s.mint_address = t.mint_address
                WHERE s.mint_address = :mint_address
                  AND s.timestamp >= :max_age_ago
                ORDER BY
                    CASE WHEN s.sol_amount >= :large_trade_threshold THEN 0 ELSE 1 END,  -- Large trades first
                    s.timestamp DESC
                LIMIT :limit
            """)

            result = self.db.execute(query, {
                'mint_address': mint_address,
                'large_trade_threshold': self.LARGE_TRADE_THRESHOLD,
                'max_age_ago': max_age_ago,
                'now': now,
                'limit': limit
            })

            large_swaps = []
            for row in result:
                sol_display = row.sol_amount / 1_000_000_000
                token_display = row.token_amount / (10 ** row.token_decimals)

                large_swaps.append(LargeSwap(
                    signature=row.signature,
                    mint_address=row.mint_address,
                    token_name=row.token_name,
                    token_symbol=row.token_symbol,
                    token_decimals=row.token_decimals,
                    timestamp=row.timestamp,
                    sol_amount=row.sol_amount,
                    token_amount=row.token_amount,
                    sol_display=sol_display,
                    token_display=token_display,
                    price_per_token=row.price_per_token,
                    swap_type=row.swap_type,
                    user_wallet=row.user_wallet,
                    is_whale_trade=row.sol_amount >= self.WHALE_TRADE_THRESHOLD,
                    is_large_trade=row.sol_amount >= self.LARGE_TRADE_THRESHOLD,
                    seconds_ago=row.seconds_ago
                ))

            return large_swaps

        except Exception as e:
            logger.error(f"Error getting large swaps for {mint_address}: {e}")
            return []